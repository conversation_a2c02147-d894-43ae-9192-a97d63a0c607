package com.sinoair.ceop.controller.innerapi.standard;

import com.sinoair.ceop.service.mabang.impl.MabangServiceImpl;
import com.sinoair.core.utils.HttpClientUtil;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Base64;

/**
 * 镖局物流通知接口测试演示类
 * 根据镖局物流通知文档实现的测试用例
 * 
 * 这个类演示了如何根据镖局物流文档生成正确的BASE64编码和MD5签名
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/07/09/14:00
 */
public class MaBangNotifyTestDemo {

    private static final String API_KEY = MabangServiceImpl.API_KEY; // 测试用的apiKey
    String url = "http://localhost:8080/innerApi/standard/mabang/receive";
    
    /**
     * MD5加密方法
     */
    public static String md5(String input) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] messageDigest = md.digest(input.getBytes());
            StringBuilder hexString = new StringBuilder();
            for (byte b : messageDigest) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            return hexString.toString();
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException(e);
        }
    }
    
    /**
     * BASE64编码方法
     */
    public static String base64Encode(String input) {
        return Base64.getEncoder().encodeToString(input.getBytes());
    }

    /**
     * 字符串重复方法（兼容Java 8）
     */
    public static String repeat(String str, int count) {
        if (count <= 0) return "";
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < count; i++) {
            sb.append(str);
        }
        return sb.toString();
    }

    /**
     * 发送HTTP请求的通用方法
     * @param requestUrl 请求URL
     * @param testName 测试名称
     */
    public static void sendHttpRequest(String requestUrl, String testName) {
        System.out.println("\n" + repeat("=", 50));
        System.out.println("开始发送HTTP请求: " + testName);
        System.out.println(repeat("=", 50));

        // 首先尝试使用HttpClientUtil
        try {
            System.out.println("请求URL: " + requestUrl);
            System.out.println("请求方法: POST (使用HttpClientUtil)");
            System.out.println("请求体: 空");
            System.out.println("发送请求中...");

            long startTime = System.currentTimeMillis();
            String response = HttpClientUtil.postContext(requestUrl, "");
            long endTime = System.currentTimeMillis();

            if (response != null && !response.trim().isEmpty()) {
                System.out.println("✅ 请求成功!");
                System.out.println("请求耗时: " + (endTime - startTime) + "ms");
                System.out.println("响应结果: " + response);

                // 简单的JSON解析（不依赖外部库）
                if (response.contains("\"code\"")) {
                    System.out.println("响应解析:");
                    if (response.contains("\"code\":20000") || response.contains("\"code\": 20000")) {
                        System.out.println("✅ 业务处理成功 (code: 20000)");
                    } else if (response.contains("\"code\":-1") || response.contains("\"code\": -1")) {
                        System.out.println("❌ 业务处理失败 (code: -1)");
                    } else {
                        System.out.println("⚠️ 未知的业务状态码");
                    }

                    // 提取message字段
                    if (response.contains("\"message\"")) {
                        int messageStart = response.indexOf("\"message\":");
                        if (messageStart != -1) {
                            String messagePart = response.substring(messageStart + 10);
                            if (messagePart.startsWith("\"")) {
                                int messageEnd = messagePart.indexOf("\"", 1);
                                if (messageEnd != -1) {
                                    String message = messagePart.substring(1, messageEnd);
                                    System.out.println("  - message: " + message);
                                }
                            } else if (messagePart.startsWith("null")) {
                                System.out.println("  - message: null");
                            }
                        }
                    }
                } else {
                    System.out.println("响应不是预期的JSON格式");
                }
            } else {
                System.out.println("❌ HttpClientUtil响应为空，尝试使用备用方法");
                sendHttpRequestFallback(requestUrl, testName);
                return;
            }

        } catch (Exception e) {
            System.out.println("❌ HttpClientUtil请求异常: " + e.getMessage());
            System.out.println("尝试使用备用HTTP客户端...");
            sendHttpRequestFallback(requestUrl, testName);
            return;
        }

        System.out.println(repeat("=", 50));
        System.out.println("HTTP请求完成: " + testName);
        System.out.println(repeat("=", 50));
    }

    /**
     * 备用HTTP请求方法（使用Java内置HTTP客户端）
     */
    public static void sendHttpRequestFallback(String requestUrl, String testName) {
        try {
            System.out.println("使用Java内置HTTP客户端发送请求...");

            URL url = new URL(requestUrl);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();

            // 设置请求方法和属性
            connection.setRequestMethod("POST");
            connection.setRequestProperty("Content-Type", "application/json");
            connection.setRequestProperty("Accept", "application/json");
            connection.setConnectTimeout(30000); // 30秒连接超时
            connection.setReadTimeout(30000);    // 30秒读取超时
            connection.setDoOutput(true);

            // 发送空的请求体
            try (OutputStream os = connection.getOutputStream()) {
                os.write("".getBytes());
                os.flush();
            }

            long startTime = System.currentTimeMillis();
            int responseCode = connection.getResponseCode();
            long endTime = System.currentTimeMillis();

            System.out.println("请求耗时: " + (endTime - startTime) + "ms");
            System.out.println("响应状态码: " + responseCode);

            // 读取响应
            BufferedReader reader;
            if (responseCode >= 200 && responseCode < 300) {
                reader = new BufferedReader(new InputStreamReader(connection.getInputStream()));
                System.out.println("✅ 备用请求成功!");
            } else {
                reader = new BufferedReader(new InputStreamReader(connection.getErrorStream()));
                System.out.println("❌ 备用请求失败!");
            }

            StringBuilder response = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                response.append(line);
            }
            reader.close();

            String responseBody = response.toString();
            System.out.println("响应结果: " + responseBody);

            // 简单的JSON解析
            if (responseBody.contains("\"code\"")) {
                System.out.println("响应解析:");
                if (responseBody.contains("\"code\":20000") || responseBody.contains("\"code\": 20000")) {
                    System.out.println("✅ 业务处理成功 (code: 20000)");
                } else if (responseBody.contains("\"code\":-1") || responseBody.contains("\"code\": -1")) {
                    System.out.println("❌ 业务处理失败 (code: -1)");
                } else {
                    System.out.println("⚠️ 未知的业务状态码");
                }
            }

        } catch (IOException e) {
            System.out.println("❌ 备用请求也失败: " + e.getMessage());
            System.out.println("异常类型: " + e.getClass().getSimpleName());

            // 提供解决建议
            if (e.getMessage() != null) {
                if (e.getMessage().contains("Connection refused") || e.getMessage().contains("ConnectException")) {
                    System.out.println("\n💡 解决建议:");
                    System.out.println("- 服务器未启动，请启动本地服务器");
                    System.out.println("- 检查端口8080是否被占用");
                    System.out.println("- 确认服务器地址是否正确");
                } else if (e.getMessage().contains("timeout")) {
                    System.out.println("\n💡 解决建议:");
                    System.out.println("- 服务器响应超时，检查服务器性能");
                    System.out.println("- 增加超时时间设置");
                }
            }
        }
    }
    
    /**
     * 测试orderChange通知
     */
    public static void testOrderChangeNotify() {
        // System.out.println(repeat("=", 60));
        System.out.println("测试orderChange通知");
        // System.out.println(repeat("=", 60));
        
        // 构建orderChange通知内容 - 使用文档示例
        String jsonContent = "{\"notify\":\"orderChange\",\"orderInfo\":{\"codes\":[\"891516700021723\",\"891516700021722\"],\"count\":2}}";
        String timestamp = "1433139940";
        
        // 进行BASE64编码
        String base64Notify = base64Encode(jsonContent);
        
        // 生成签名
        String signString = "notify=" + base64Notify + "&timestamp=" + timestamp + API_KEY;
        String sign = md5(signString);
        
        // 构建请求URL
        String requestUrl = "http://localhost:8080/innerApi/standard/mabang/receive?notify=" + base64Notify + "&timestamp=" + timestamp + "&sign=" + sign;
        
        System.out.println("原始JSON内容: " + jsonContent);
        System.out.println("BASE64编码后: " + base64Notify);
        System.out.println("签名字符串: " + signString);
        System.out.println("MD5签名: " + sign);
        System.out.println("完整请求URL: " + requestUrl);

        // 验证与文档示例的一致性
        String expectedBase64 = "eyJub3RpZnkiOiJvcmRlckNoYW5nZSIsIm9yZGVySW5mbyI6eyJjb2RlcyI6WyI4OTE1MTY3MDAwMjE3MjMiLCI4OTE1MTY3MDAwMjE3MjIiXSwiY291bnQiOjJ9fQ==";
        String expectedSign = "7f326b45af63ba7cfaa6a2af164a525d";

        System.out.println("\n文档期望结果对比:");
        System.out.println("期望BASE64: " + expectedBase64);
        System.out.println("实际BASE64: " + base64Notify);
        System.out.println("BASE64匹配: " + expectedBase64.equals(base64Notify));

        System.out.println("期望签名: " + expectedSign);
        System.out.println("实际签名: " + sign);
        System.out.println("签名匹配: " + expectedSign.equals(sign));

        // 发送真实的POST请求
        sendHttpRequest(requestUrl, "orderChange通知");
    }
    
    /**
     * 测试refreshToken通知 - 平台店铺授权
     */
    public static void testRefreshTokenPlatform() {
        // System.out.println("\n" + repeat("=", 60));
        System.out.println("测试refreshToken通知 - 平台店铺授权");
        // System.out.println(repeat("=", 60));
        
        // 构建refreshToken通知内容
        String jsonContent = "{\"notify\":\"refreshToken\",\"type\":\"platform\",\"sourceId\":\"TEST_SOURCE_ID_001\",\"site\":\"US\"}";
        String timestamp = String.valueOf(System.currentTimeMillis() / 1000);
        
        // 进行BASE64编码
        String base64Notify = base64Encode(jsonContent);
        
        // 生成签名
        String signString = "notify=" + base64Notify + "&timestamp=" + timestamp + API_KEY;
        String sign = md5(signString);
        
        // 构建请求URL
        String requestUrl = "http://localhost:8080/innerApi/standard/mabang/receive?notify=" + base64Notify + "&timestamp=" + timestamp + "&sign=" + sign;
        
        System.out.println("原始JSON内容: " + jsonContent);
        System.out.println("BASE64编码后: " + base64Notify);
        System.out.println("签名字符串: " + signString);
        System.out.println("MD5签名: " + sign);
        System.out.println("完整请求URL: " + requestUrl);
        System.out.println("请求URL构建完成，可用于实际HTTP请求测试");

        // 发送真实的POST请求
        sendHttpRequest(requestUrl, "refreshToken通知 - 平台店铺授权");
        // 模拟响应
        System.out.println("\n期望的响应格式:");
        String responseExample = "{\n" +
                "  \"ErrorCode\": \"9999\",\n" +
                "  \"Message\": \"成功\",\n" +
                "  \"Data\": {\n" +
                "    \"sourceId\": \"TEST_SOURCE_ID_001\",\n" +
                "    \"accessToken\": \"mock_access_token_12345\",\n" +
                "    \"platformShopId\": \"mock_shop_id_67890\"\n" +
                "  }\n" +
                "}";
        System.out.println(responseExample);
    }
    
    /**
     * 测试refreshToken通知 - 直发物流授权
     */
    public static void testRefreshTokenLogistics() {
        System.out.println("\n" + repeat("=", 60));
        System.out.println("测试refreshToken通知 - 直发物流授权");
        System.out.println(repeat("=", 60));
        
        // 构建refreshToken通知内容
        String jsonContent = "{\"notify\":\"refreshToken\",\"type\":\"logistics\",\"sourceId\":\"TEST_SOURCE_ID_002\",\"site\":\"UK\"}";
        String timestamp = String.valueOf(System.currentTimeMillis() / 1000);
        
        // 进行BASE64编码
        String base64Notify = base64Encode(jsonContent);
        
        // 生成签名
        String signString = "notify=" + base64Notify + "&timestamp=" + timestamp + API_KEY;
        String sign = md5(signString);
        
        // 构建请求URL
        String requestUrl = "http://localhost:8080/innerApi/standard/mabang/receive?notify=" + base64Notify + "&timestamp=" + timestamp + "&sign=" + sign;
        
        System.out.println("原始JSON内容: " + jsonContent);
        System.out.println("BASE64编码后: " + base64Notify);
        System.out.println("签名字符串: " + signString);
        System.out.println("MD5签名: " + sign);
        System.out.println("完整请求URL: " + requestUrl);

        // 发送真实的POST请求
        sendHttpRequest(requestUrl, "refreshToken通知 - 直发物流授权");
    }
    
    /**
     * 测试签名验证失败的情况
     */
    public static void testInvalidSignature() {
        System.out.println("\n" + repeat("=", 60));
        System.out.println("测试签名验证失败的情况");
        System.out.println(repeat("=", 60));
        
        String jsonContent = "{\"notify\":\"orderChange\",\"orderInfo\":{\"codes\":[\"TEST-ORDER-001\"],\"count\":1}}";
        String timestamp = String.valueOf(System.currentTimeMillis() / 1000);
        String base64Notify = base64Encode(jsonContent);
        
        // 正确的签名
        String correctSignString = "notify=" + base64Notify + "&timestamp=" + timestamp + API_KEY;
        String correctSign = md5(correctSignString);
        
        // 错误的签名
        String wrongSign = "wrong_signature_for_test";
        
        System.out.println("原始JSON内容: " + jsonContent);
        System.out.println("BASE64编码后: " + base64Notify);
        System.out.println("正确的签名字符串: " + correctSignString);
        System.out.println("正确的MD5签名: " + correctSign);
        System.out.println("错误的签名: " + wrongSign);
        System.out.println("签名验证结果: " + (correctSign.equals(wrongSign) ? "通过" : "失败"));
        
        String correctUrl = "http://localhost:8080/innerApi/standard/mabang/receive?notify=" + base64Notify + "&timestamp=" + timestamp + "&sign=" + correctSign;
        String wrongUrl = "http://localhost:8080/innerApi/standard/mabang/receive?notify=" + base64Notify + "&timestamp=" + timestamp + "&sign=" + wrongSign;

        System.out.println("正确的请求URL: " + correctUrl);
        System.out.println("错误的请求URL: " + wrongUrl);

        // 先发送正确的请求
        sendHttpRequest(correctUrl, "签名验证测试 - 正确签名");

        // 再发送错误的请求
        sendHttpRequest(wrongUrl, "签名验证测试 - 错误签名");
    }
    
    /**
     * 测试BASE64中包含加号的情况
     */
    public static void testBase64WithPlusSign() {
        System.out.println("\n" + repeat("=", 60));
        System.out.println("测试BASE64中包含加号的情况");
        System.out.println(repeat("=", 60));
        
        // 构建一个会产生包含加号的BASE64编码的JSON
        String jsonContent = "{\"notify\":\"orderChange\",\"orderInfo\":{\"codes\":[\"TEST+ORDER+WITH+PLUS+SIGNS\"],\"count\":1}}";
        String timestamp = String.valueOf(System.currentTimeMillis() / 1000);
        
        String base64Notify = base64Encode(jsonContent);
        
        System.out.println("原始JSON内容: " + jsonContent);
        System.out.println("BASE64编码: " + base64Notify);
        System.out.println("是否包含加号: " + base64Notify.contains("+"));
        
        if (base64Notify.contains("+")) {
            System.out.println("注意：BASE64结果包含加号，网络传输时可能被转换为空格");
            System.out.println("需要在验证签名时将空格转回加号");
            
            String base64WithSpaces = base64Notify.replace("+", " ");
            System.out.println("模拟网络传输后的BASE64: " + base64WithSpaces);
            System.out.println("恢复加号后的BASE64: " + base64WithSpaces.replace(" ", "+"));
        }
        
        String signString = "notify=" + base64Notify + "&timestamp=" + timestamp + API_KEY;
        String sign = md5(signString);

        System.out.println("签名字符串: " + signString);
        System.out.println("MD5签名: " + sign);

        // 构建请求URL并发送请求
        String requestUrl = "http://localhost:8080/innerApi/standard/mabang/receive?notify=" + base64Notify + "&timestamp=" + timestamp + "&sign=" + sign;
        sendHttpRequest(requestUrl, "BASE64包含加号测试");
    }

    /**
     * 测试批量发送请求
     */
    public static void testBatchRequests() {
        System.out.println("\n" + repeat("=", 60));
        System.out.println("测试批量发送请求");
        System.out.println(repeat("=", 60));

        // 模拟多个订单变更通知
        String[] orderCodes = {
            "TEST-ORDER-001", "TEST-ORDER-002", "TEST-ORDER-003"
        };

        for (int i = 0; i < orderCodes.length; i++) {
            String jsonContent = "{\"notify\":\"orderChange\",\"orderInfo\":{\"codes\":[\"" + orderCodes[i] + "\"],\"count\":1}}";
            String timestamp = String.valueOf(System.currentTimeMillis() / 1000);
            String base64Notify = base64Encode(jsonContent);
            String signString = "notify=" + base64Notify + "&timestamp=" + timestamp + API_KEY;
            String sign = md5(signString);
            String requestUrl = "http://localhost:8080/innerApi/standard/mabang/receive?notify=" + base64Notify + "&timestamp=" + timestamp + "&sign=" + sign;

            System.out.println("\n批量请求 " + (i + 1) + "/" + orderCodes.length + ":");
            System.out.println("订单号: " + orderCodes[i]);
            sendHttpRequest(requestUrl, "批量订单变更通知 - " + orderCodes[i]);

            // 添加延迟避免请求过于频繁
            try {
                Thread.sleep(1000); // 1秒延迟
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }
    }

    /**
     * 测试服务器连接性
     */
    public static void testServerConnectivity() {
        System.out.println("\n" + repeat("=", 60));
        System.out.println("测试服务器连接性");
        System.out.println(repeat("=", 60));

        // 发送一个简单的测试请求
        String jsonContent = "{\"notify\":\"orderChange\",\"orderInfo\":{\"codes\":[\"CONNECTIVITY-TEST\"],\"count\":1}}";
        String timestamp = String.valueOf(System.currentTimeMillis() / 1000);
        String base64Notify = base64Encode(jsonContent);
        String signString = "notify=" + base64Notify + "&timestamp=" + timestamp + API_KEY;
        String sign = md5(signString);
        String requestUrl = "http://localhost:8080/innerApi/standard/mabang/receive?notify=" + base64Notify + "&timestamp=" + timestamp + "&sign=" + sign;

        System.out.println("测试服务器是否可达...");
        sendHttpRequest(requestUrl, "服务器连接性测试");
    }

    /**
     * 主方法 - 运行所有测试
     */
    public static void main(String[] args) {
        System.out.println("镖局物流通知接口测试演示");
        System.out.println("根据镖局物流通知文档实现");
        System.out.println("API Key: " + API_KEY);
        
        // 首先测试服务器连接性
        testServerConnectivity();

        // 运行所有测试
        testOrderChangeNotify();
        testRefreshTokenPlatform();
        testRefreshTokenLogistics();
        testInvalidSignature();
        testBase64WithPlusSign();

        // 批量测试
        testBatchRequests();

        System.out.println("\n" + repeat("=", 60));
        System.out.println("所有测试完成");
        System.out.println(repeat("=", 60));

        System.out.println("\n关键修复点总结:");
        System.out.println("1. ❌ 原实现：对notify参数再次进行BASE64编码");
        System.out.println("2. ✅ 正确实现：notify参数已经是BASE64编码，直接使用");
        System.out.println("3. ✅ 签名验证：使用BASE64编码的notify参数进行签名");
        System.out.println("4. ✅ 内容解析：对BASE64编码的notify参数进行解码获取JSON");
        System.out.println("5. ✅ 异常处理：添加完善的异常处理和日志记录");
        System.out.println("6. ✅ HTTP请求：使用HttpClientUtil.postContext发送真实请求");
        System.out.println("7. ✅ 请求格式：POST方法，参数在URL查询字符串中，请求体为空");

        System.out.println("\nHTTP请求说明:");
        System.out.println("- 请求方法: POST");
        System.out.println("- 参数位置: URL查询字符串");
        System.out.println("- 请求体: 空字符串");
        System.out.println("- 响应格式: JSON");
        System.out.println("- 成功响应: {\"code\":20000,\"data\":\"\",\"message\":null,\"timestamp\":\"...\"}");
        System.out.println("- 失败响应: {\"code\":-1,\"message\":\"错误信息\",\"timestamp\":\"...\"}");

        System.out.println("\n使用方法:");
        System.out.println("1. 确保本地服务器运行在 http://localhost:8080");
        System.out.println("2. 运行此测试类的main方法");
        System.out.println("3. 观察控制台输出的请求和响应信息");
        System.out.println("4. 检查服务器日志确认请求被正确处理");
    }
}
