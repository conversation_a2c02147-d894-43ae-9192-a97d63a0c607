package com.sinoair.ceop.service;

import com.sinoair.ceop.dao.PackagesMapper;
import com.sinoair.ceop.domain.model.Packages;
import com.sinoair.ceop.domain.vo.ReturnObject;
import com.sinoair.ceop.testUtil.CommonTestCase;
import org.junit.Test;

import javax.annotation.Resource;

public class SortPkgPrintCodeServiceTest extends CommonTestCase {

    @Resource(name = "PackagesMapper")
    PackagesMapper packagesMapper;

    @Test
    public void scanOriginalPkgPrintCode() throws Exception {
        ReturnObject returnObject = new ReturnObject();
        String originalPackageCode = "L00006195171";
        Packages destinationPackages = packagesMapper.queryPackagesByPkgSyscode("7209808");
        //检查原始袋子和目的袋子不能相同
        if (originalPackageCode.equals(destinationPackages.getPkgPrintcode())) {
            System.out.println("原始袋子和目的袋子相同，不允许操作");

        }
    }

}