# 数据库驱动jar 路径
#drive.class.path=C:\\Users\\<USER>\\.m2\\repository\\com\\oracle\\ojdbc14\\10.2.0.1.0\\ojdbc14-10.2.0.1.0.jar
drive.class.path=D:\\softwareData\\maven\\repository\\oracle\\ojdbc\\6.0\\ojdbc-6.0.jar
#drive.class.path=D:\\.m2\\repository\\oracle\\ojdbc\\6.0\\ojdbc-6.0.jar
#drive.class.path=/Users/<USER>/tools/maven/repository/oracle/ojdbc/6.0/ojdbc-6.0.jar
#drive.class.path=C:\\Users\\<USER>\\.m2\\repository\\oracle\\ojdbc\\6.0\\ojdbc-6.0.jar
# 数据库连接参数
jdbc.driver=oracle.jdbc.driver.OracleDriver
jdbc.url=********************************************
jdbc.username=CEOP
jdbc.password=CEOP
# 包路径配置
model.package=com.sinoair.ceop.domain.model
dao.package=com.sinoair.ceop.dao
xml.mapper.package=mapper

#放在test中生成 再转移到main 以防覆盖
target.project=src/test/java
