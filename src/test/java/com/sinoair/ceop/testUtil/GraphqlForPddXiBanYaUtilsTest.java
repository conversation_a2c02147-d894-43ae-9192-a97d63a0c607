package com.sinoair.ceop.testUtil;

import com.alibaba.fastjson.JSONObject;
import com.sinoair.ceop.domain.model.Eawbpre;
import com.sinoair.ceop.domain.vo.ReturnObject;
import com.sinoair.ceop.domain.vo.graphqlPdd.GraphqlForPddXiBanYaUtils;
import junit.framework.TestCase;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;


/**
 * <AUTHOR> 2023-05-23 15:46
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration("classpath:config/applicationContext.xml")
@WebAppConfiguration
public class GraphqlForPddXiBanYaUtilsTest extends TestCase {
    /**
     * 1、获取token测试
     *
     * @throws Exception
     */
    @Test
    public void getClientToken() throws Exception {
        ReturnObject clientToken = GraphqlForPddXiBanYaUtils.getClientToken();
        JSONObject token = (JSONObject) clientToken.getData();
        System.out.println("响应结果:" + JSONObject.toJSONString(token));
    }

    /**
     * 2、下单接口
     *
     * @throws Exception
     */
    @Test
    public void createShipment() throws Exception {
        Eawbpre eawbpre = new Eawbpre();
        String token = "xxxxx";
        ReturnObject shipment = GraphqlForPddXiBanYaUtils.createShipment(token, eawbpre);
        JSONObject jsonObject = (JSONObject) shipment.getData();
        System.out.println("响应结果:" + JSONObject.toJSONString(jsonObject));
    }


    /**
     * 2、获取面单PDF接口
     *
     * @throws Exception
     */
    @Test
    public void findShipment() throws Exception {
        Eawbpre eawbpre = new Eawbpre();
        String token = "xxxxx";
        ReturnObject returnObject = GraphqlForPddXiBanYaUtils.findShipment(token, eawbpre);
        System.out.println("响应结果:" + JSONObject.toJSONString(returnObject));
    }
}
