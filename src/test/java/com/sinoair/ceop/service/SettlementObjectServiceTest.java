package com.sinoair.ceop.service;

import com.sinoair.ceop.domain.model.SettlementObject;
import com.sinoair.ceop.testUtil.CommonTestCase;
import org.junit.Assert;
import org.junit.Test;

import javax.annotation.Resource;

import java.util.Date;
import java.util.List;

import static org.junit.Assert.*;

/**
 * Created by XueQB on 2017/3/3.
 */
public class SettlementObjectServiceTest extends CommonTestCase {

    @Resource
    SettlementObjectService settlementObjectService;



    @Test
    public void insertSelective() throws Exception {
//        Integer id = 10;
        SettlementObject settlementObject = new SettlementObject();
        settlementObject.setSoSyscode(10);
        settlementObject.setSoCode("777778");
        settlementObject.setSacId("SNR");
        settlementObject.setcCode("SNR90974");
        settlementObject.setSoName("0%-亚马逊测试账号");
        settlementObject.setSoEname("TEST");
        settlementObject.setSoStatus("ON");
        settlementObject.setSoSacCode("SNR");
        settlementObject.setSoEIdHandler(1);
        settlementObject.setSoHandletime(new Date());
        settlementObject.setSoArchivetime(new Date());
        settlementObjectService.insertSelective(settlementObject);
//        SettlementObject settlementObject2 = settlementObjectService.selectById(id);
//        Assert.assertEquals("插入完成",settlementObject2.getSoSyscode(),id);
    }


    @Test
    public void update(){
        Integer id = 10;
        SettlementObject settlementObject = new SettlementObject();
        settlementObject.setSoSyscode(10);
        settlementObject.setSoCode("7777780");
        settlementObject.setSacId("SNR0");
        settlementObject.setcCode("SNR909740");
        settlementObject.setSoName("0%-亚马逊测试账号0");
        settlementObject.setSoEname("TEST0");
        settlementObject.setSoStatus("ON");
        settlementObject.setSoSacCode("SNR0");
        settlementObject.setSoEIdHandler(1);
        settlementObject.setSoHandletime(new Date());
        settlementObject.setSoArchivetime(new Date());
        settlementObjectService.updateByPrimaryKeySelective(settlementObject);
        SettlementObject settlementObject2 = settlementObjectService.selectById(id);
        Assert.assertEquals("插入完成",settlementObject2.getSoEname(),"TEST0");
    }

    @Test
    public void search() throws Exception {
        SettlementObject settlementObject = new SettlementObject();
        settlementObject.setSacId("HGH");
        List<SettlementObject> settlementObjectList = settlementObjectService.search(settlementObject);
        String sacId = settlementObjectList.get(0).getSacId();
        Assert.assertEquals("查询到了",sacId,"HGH");

    }

    @Test
    public void selectById() throws Exception {
        Integer id = 10;
        SettlementObject settlementObject = settlementObjectService.selectById(id);
        Assert.assertEquals("正确",settlementObject.getSoSyscode(),id);
    }

    @Test
    public void deleteById() throws Exception {
        int id = 10;
        settlementObjectService.deleteById(id);
    }
}