package com.sinoair.ceop.service;

import com.sinoair.ceop.dao.ExpressairwaybillMapper;
import com.sinoair.ceop.domain.model.Expressairwaybill;
import com.sinoair.ceop.domain.model.Expressassignment;
import com.sinoair.ceop.testUtil.CommonTestCase;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.List;

/**
 * Created by JiaZF on 2017/6/14.
 */
public class ExpressAirWayServiceTest extends CommonTestCase {

    @Resource
    ExpressairwaybillMapper expressairwaybillMapper;

    @Resource
    ExpressassignmentService expressassignmentService;

    @Test
    public void test1() {
        List<Expressairwaybill> eawbListByEaCode4Lazada = expressairwaybillMapper.findEawbListByEaCode4Lazada("784-46552995");
        for (int i = 0; i < eawbListByEaCode4Lazada.size(); i++) {
            System.out.println("---------");
        }
    }

    @Test
    public void test2() {
        Expressassignment eamByEaCode4Lazada = expressassignmentService.findEamByEaCode4Lazada("784-46552995");
        System.out.println(eamByEaCode4Lazada);
    }
}
