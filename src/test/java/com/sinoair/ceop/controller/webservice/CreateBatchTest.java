package com.sinoair.ceop.controller.webservice;

import com.alibaba.fastjson.JSONObject;
import com.sinoair.ceop.dao.ExpressairwaybillMapper;
import com.sinoair.ceop.domain.model.Expressairwaybill;
import com.sinoair.ceop.domain.vo.KoreaYunDa.creatBatch.BatchCreateUtil;
import com.sinoair.ceop.testUtil.CommonTestCase;
import com.sinoair.core.labelsupport.base.exception.SinoairException;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 创建批次、请求订单接口测试类
 *
 * <AUTHOR> 2023-03-22 21:46
 */
public class CreateBatchTest extends CommonTestCase {

    @Autowired
    private ExpressairwaybillMapper expressairwaybillMapper;

    @Autowired
    private BatchCreateUtil batchCreateUtil;

    @Test
    public void test() throws SinoairException {
        //韩国韵达测试
//        Expressairwaybill eawb = expressairwaybillMapper.queryError("6027036994130");
//        /**
//         * {"error":0,"message":"success","data":{"batch_no":"1875094","start_time":"2023-03-23 14:37:22","end_time":"2023-03-24 14:37:22","batch_name":"20190113第一批订单"}}
//         */
//        String batchRes = batchCreateUtil.toCreateBatch("20190113第一批订单", eawb);
//        System.out.println("创建批次响应结果:" + batchRes);
//        JSONObject jsonObject = JSONObject.parseObject(batchRes);
//        String batch_no = jsonObject.getJSONObject("data").getString("batch_no");
//        String orderRes = batchCreateUtil.toCreateOrder("1875723", eawb);
//        System.out.println("请求订单响应结果:" + orderRes);
        //韩国韵达测试
    }

}
