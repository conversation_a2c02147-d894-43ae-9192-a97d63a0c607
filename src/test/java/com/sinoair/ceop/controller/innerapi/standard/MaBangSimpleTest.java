package com.sinoair.ceop.controller.innerapi.standard;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Base64;

/**
 * 镖局物流简单HTTP测试
 * 专门用于验证HTTP请求发送功能
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/07/09/14:00
 */
public class MaBangSimpleTest {

    // 网站的上key用来测试。实际的请参考mabang
    private static final String API_KEY = "6168f305888c3d795e67c6de17bf8a21";
    
    public static String md5(String input) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] messageDigest = md.digest(input.getBytes());
            StringBuilder hexString = new StringBuilder();
            for (byte b : messageDigest) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            return hexString.toString();
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException(e);
        }
    }
    
    public static String base64Encode(String input) {
        return Base64.getEncoder().encodeToString(input.getBytes());
    }

    public static void sendHttpRequest(String requestUrl) {
        System.out.println("==================================================");
        System.out.println("开始发送HTTP请求");
        System.out.println("==================================================");
        
        try {
            System.out.println("请求URL: " + requestUrl);
            
            URL url = new URL(requestUrl);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            
            connection.setRequestMethod("POST");
            connection.setRequestProperty("Content-Type", "application/json");
            connection.setConnectTimeout(30000);
            connection.setReadTimeout(30000);
            connection.setDoOutput(true);
            
            try (OutputStream os = connection.getOutputStream()) {
                os.write("".getBytes());
                os.flush();
            }
            
            int responseCode = connection.getResponseCode();
            System.out.println("响应状态码: " + responseCode);
            
            BufferedReader reader;
            if (responseCode >= 200 && responseCode < 300) {
                reader = new BufferedReader(new InputStreamReader(connection.getInputStream()));
                System.out.println("✅ 请求成功!");
            } else {
                reader = new BufferedReader(new InputStreamReader(connection.getErrorStream()));
                System.out.println("❌ 请求失败!");
            }
            
            StringBuilder response = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                response.append(line);
            }
            reader.close();
            
            String responseBody = response.toString();
            System.out.println("响应结果: " + responseBody);
            
            if (responseBody.contains("\"code\":20000")) {
                System.out.println("✅ 业务处理成功");
            } else if (responseBody.contains("\"code\":-1")) {
                System.out.println("❌ 业务处理失败");
            }
            
        } catch (IOException e) {
            System.out.println("❌ 请求异常: " + e.getMessage());
            if (e.getMessage().contains("Connection refused")) {
                System.out.println("💡 服务器未启动，请启动本地服务器");
            }
        }
        
        System.out.println("==================================================");
        System.out.println("HTTP请求完成");
        System.out.println("==================================================");
    }
    
    public static void main(String[] args) {
        System.out.println("镖局物流简单HTTP测试");
        System.out.println("API Key: " + API_KEY);
        
        // 构建测试请求
        String jsonContent = "{\"notify\":\"orderChange\",\"orderInfo\":{\"codes\":[\"TEST-001\"],\"count\":1}}";
        String timestamp = String.valueOf(System.currentTimeMillis() / 1000);
        String base64Notify = base64Encode(jsonContent);
        String signString = "notify=" + base64Notify + "&timestamp=" + timestamp + API_KEY;
        String sign = md5(signString);
        String requestUrl = "http://localhost:8080/innerApi/standard/mabang/receive?notify=" + base64Notify + "&timestamp=" + timestamp + "&sign=" + sign;
        
        System.out.println("原始JSON: " + jsonContent);
        System.out.println("BASE64编码: " + base64Notify);
        System.out.println("MD5签名: " + sign);
        
        // 发送HTTP请求
        sendHttpRequest(requestUrl);
        
        System.out.println("\n测试完成!");
    }
}
