package com.sinoair.ceop.controller.innerapi.standard;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Base64;

/**
 * 镖局物流HTTP请求测试演示类
 * 专门用于测试HTTP请求发送功能
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/07/09/14:00
 */
public class MaBangHttpTestDemo {

    private static final String API_KEY = "6168f305888c3d795e67c6de17bf8a21"; // 测试用的apiKey
    
    /**
     * MD5加密方法
     */
    public static String md5(String input) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] messageDigest = md.digest(input.getBytes());
            StringBuilder hexString = new StringBuilder();
            for (byte b : messageDigest) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            return hexString.toString();
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException(e);
        }
    }
    
    /**
     * BASE64编码方法
     */
    public static String base64Encode(String input) {
        return Base64.getEncoder().encodeToString(input.getBytes());
    }

    /**
     * 字符串重复方法（兼容Java 8）
     */
    public static String repeat(String str, int count) {
        if (count <= 0) return "";
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < count; i++) {
            sb.append(str);
        }
        return sb.toString();
    }
    
    /**
     * 发送HTTP请求的方法（使用Java内置HTTP客户端）
     */
    public static void sendHttpRequest(String requestUrl, String testName) {
        System.out.println("\n" + repeat("=", 50));
        System.out.println("开始发送HTTP请求: " + testName);
        System.out.println(repeat("=", 50));

        try {
            System.out.println("请求URL: " + requestUrl);
            System.out.println("请求方法: POST");
            System.out.println("请求体: 空");
            System.out.println("发送请求中...");

            URL url = new URL(requestUrl);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();

            // 设置请求方法和属性
            connection.setRequestMethod("POST");
            connection.setRequestProperty("Content-Type", "application/json");
            connection.setRequestProperty("Accept", "application/json");
            connection.setConnectTimeout(30000); // 30秒连接超时
            connection.setReadTimeout(30000);    // 30秒读取超时
            connection.setDoOutput(true);

            // 发送空的请求体
            try (OutputStream os = connection.getOutputStream()) {
                os.write("".getBytes());
                os.flush();
            }

            long startTime = System.currentTimeMillis();
            int responseCode = connection.getResponseCode();
            long endTime = System.currentTimeMillis();

            System.out.println("请求耗时: " + (endTime - startTime) + "ms");
            System.out.println("响应状态码: " + responseCode);

            // 读取响应
            BufferedReader reader;
            if (responseCode >= 200 && responseCode < 300) {
                reader = new BufferedReader(new InputStreamReader(connection.getInputStream()));
                System.out.println("✅ 请求成功!");
            } else {
                reader = new BufferedReader(new InputStreamReader(connection.getErrorStream()));
                System.out.println("❌ 请求失败!");
            }

            StringBuilder response = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                response.append(line);
            }
            reader.close();

            String responseBody = response.toString();
            System.out.println("响应结果: " + responseBody);

            // 简单的JSON解析（不依赖外部库）
            if (responseBody.contains("\"code\"")) {
                System.out.println("响应解析:");
                if (responseBody.contains("\"code\":20000") || responseBody.contains("\"code\": 20000")) {
                    System.out.println("✅ 业务处理成功 (code: 20000)");
                } else if (responseBody.contains("\"code\":-1") || responseBody.contains("\"code\": -1")) {
                    System.out.println("❌ 业务处理失败 (code: -1)");
                } else {
                    System.out.println("⚠️ 未知的业务状态码");
                }

                // 提取message字段
                if (responseBody.contains("\"message\"")) {
                    int messageStart = responseBody.indexOf("\"message\":");
                    if (messageStart != -1) {
                        String messagePart = responseBody.substring(messageStart + 10);
                        if (messagePart.startsWith("\"")) {
                            int messageEnd = messagePart.indexOf("\"", 1);
                            if (messageEnd != -1) {
                                String message = messagePart.substring(1, messageEnd);
                                System.out.println("  - message: " + message);
                            }
                        } else if (messagePart.startsWith("null")) {
                            System.out.println("  - message: null");
                        }
                    }
                }
            } else {
                System.out.println("响应不是预期的JSON格式");
            }

        } catch (IOException e) {
            System.out.println("❌ 请求异常: " + e.getMessage());
            System.out.println("异常类型: " + e.getClass().getSimpleName());

            // 提供解决建议
            if (e.getMessage() != null) {
                if (e.getMessage().contains("Connection refused") || e.getMessage().contains("ConnectException")) {
                    System.out.println("\n💡 解决建议:");
                    System.out.println("- 服务器未启动，请启动本地服务器");
                    System.out.println("- 检查端口8080是否被占用");
                    System.out.println("- 确认服务器地址是否正确");
                } else if (e.getMessage().contains("timeout")) {
                    System.out.println("\n💡 解决建议:");
                    System.out.println("- 服务器响应超时，检查服务器性能");
                    System.out.println("- 增加超时时间设置");
                } else if (e.getMessage().contains("UnknownHostException")) {
                    System.out.println("\n💡 解决建议:");
                    System.out.println("- 检查网络连接");
                    System.out.println("- 确认主机名是否正确");
                }
            }
        } catch (Exception e) {
            System.out.println("❌ 其他异常: " + e.getMessage());
            System.out.println("异常类型: " + e.getClass().getSimpleName());
        }

        System.out.println(repeat("=", 50));
        System.out.println("HTTP请求完成: " + testName);
        System.out.println(repeat("=", 50));
    }
    
    /**
     * 测试单个HTTP请求
     */
    public static void testSingleRequest() {
        System.out.println("测试单个HTTP请求");
        
        // 构建orderChange通知内容 - 使用文档示例
        String jsonContent = "{\"notify\":\"orderChange\",\"orderInfo\":{\"codes\":[\"891516700021723\",\"891516700021722\"],\"count\":2}}";
        String timestamp = "1433139940";
        
        // 进行BASE64编码
        String base64Notify = base64Encode(jsonContent);
        
        // 生成签名
        String signString = "notify=" + base64Notify + "&timestamp=" + timestamp + API_KEY;
        String sign = md5(signString);
        
        // 构建请求URL
        String requestUrl = "http://localhost:8080/innerApi/standard/mabang/receive?notify=" + base64Notify + "&timestamp=" + timestamp + "&sign=" + sign;
        
        System.out.println("原始JSON内容: " + jsonContent);
        System.out.println("BASE64编码后: " + base64Notify);
        System.out.println("签名字符串: " + signString);
        System.out.println("MD5签名: " + sign);
        System.out.println("完整请求URL: " + requestUrl);
        
        // 发送HTTP请求
        sendHttpRequest(requestUrl, "orderChange通知测试");
    }
    
    /**
     * 测试错误签名的请求
     */
    public static void testWrongSignature() {
        System.out.println("\n测试错误签名的请求");
        
        String jsonContent = "{\"notify\":\"orderChange\",\"orderInfo\":{\"codes\":[\"TEST-ORDER-001\"],\"count\":1}}";
        String timestamp = String.valueOf(System.currentTimeMillis() / 1000);
        String base64Notify = base64Encode(jsonContent);
        String wrongSign = "wrong_signature_for_test";
        
        String requestUrl = "http://localhost:8080/innerApi/standard/mabang/receive?notify=" + base64Notify + "&timestamp=" + timestamp + "&sign=" + wrongSign;
        
        System.out.println("使用错误签名的请求URL: " + requestUrl);
        
        // 发送HTTP请求
        sendHttpRequest(requestUrl, "错误签名测试");
    }
    
    /**
     * 主方法
     */
    public static void main(String[] args) {
        System.out.println("镖局物流HTTP请求测试演示");
        System.out.println("API Key: " + API_KEY);
        System.out.println("目标服务器: http://localhost:8080");
        
        // 测试正确的请求
        testSingleRequest();
        
        // 测试错误签名的请求
        testWrongSignature();
        
        System.out.println("\n" + repeat("=", 60));
        System.out.println("HTTP测试完成");
        System.out.println(repeat("=", 60));
        
        System.out.println("\n使用说明:");
        System.out.println("1. 确保本地服务器运行在 http://localhost:8080");
        System.out.println("2. 运行此测试类观察HTTP请求和响应");
        System.out.println("3. 正确的请求应该返回 code:20000");
        System.out.println("4. 错误签名的请求应该返回 code:-1");
    }
}
