package com.sinoair.ceop.testUtil;

import com.sinoair.ceop.domain.model.WcDomesticShipment;
import com.sinoair.ceop.domain.model.WcDsTrack;
import com.sinoair.core.cainiao.CaiNiaoReturnUtil;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2025-06-17 17:01
 * @{description}
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration("classpath:config/applicationContext.xml")
@WebAppConfiguration
// 单元测试类
public class CaiNiaoReturnUtilTest {
    @Test
    public void testConsoPustOrderTraceRes() throws Exception {
        WcDsTrack wcDsTrack = new WcDsTrack();
        wcDsTrack.setEastCode("HANDOVER");
        wcDsTrack.setOccurTime(new Date());
        wcDsTrack.setBarcode("TEST_BARCODE");

        WcDomesticShipment wcDomesticShipment = new WcDomesticShipment();
        wcDomesticShipment.setInternalBarcode("INTERNAL_BARCODE");
        wcDomesticShipment.setLength(new BigDecimal(10));
        wcDomesticShipment.setWidth(new BigDecimal(5));
        wcDomesticShipment.setHeight(new BigDecimal(2));
        wcDomesticShipment.setGrossWeight(new BigDecimal(1));
        wcDomesticShipment.setCategoryFeature("001");
        wcDomesticShipment.setCnCategoryFeature("A");

        String interFaceType = "CONSO_WAREHOUSE_SIGN";
        String trunkCode = "TRUNK_123";

        // 这里只做调用，不断言返回值
        try {
            //CaiNiaoReturnUtil.consoPustOrderTraceRes(wcDsTrack, wcDomesticShipment, interFaceType);
        } catch (Exception e) {
            assert false : "方法抛出异常";
        }
    }
}