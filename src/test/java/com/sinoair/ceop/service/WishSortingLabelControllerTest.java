package com.sinoair.ceop.service;

import com.alibaba.fastjson.JSONObject;
import com.sinoair.ceop.controller.webservice.wish.WishSortingLabelController;
import com.sinoair.ceop.domain.vo.ReturnObject;
import com.sinoair.ceop.domain.vo.ServiceReturn;
import com.sinoair.ceop.domain.vo.wish.sorting.SortingReturn;
import com.sinoair.ceop.domain.vo.wish.sorting.WishSortingGetLabelParam;
import com.sinoair.ceop.service.sorting.WishSortingLabelService;
import com.sinoair.ceop.testUtil.CommonTestCase;
import com.sinoair.core.utils.HttpClientUtil;
import org.junit.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import javax.servlet.http.HttpServletRequest;
import java.util.UUID;

/**
 * Created by JunFei on 2021/3/4.
 */
public class WishSortingLabelControllerTest extends CommonTestCase {

    private Logger logger = LoggerFactory.getLogger(WishSortingLabelController.class);

    @Autowired
    private WishSortingLabelService wishSortingLabelService;
    /**
     * 获取面单接口
     * @return
     */
    @Test
    public void getLabel(){
        SortingReturn ro = new SortingReturn();
        ro.setSuccess(true);
        ro.setMessageId(UUID.randomUUID().toString());
        try {
            String reqBody = "{\"data\":{\"facilitycode\":\"08\",\"trackingNumber\":\"SE102231793754\",\"referenceNumber\":\"SE102231793754\",\"packageWeight\":\"1328\",\"operateUser\":\"hgh-caiqun\",\"scanPackageTime\":\"2021-03-24 09:39:17\",\"packageLength\":null,\"packageWidth\":null,\"packageHeight\":null},\"messageId\":\"20210303163917\",\"timestamp\":\"1614760757000\"}";

            JSONObject jo = JSONObject.parseObject(reqBody);
            WishSortingGetLabelParam wishSortingGetLabelParam = jo.getObject("data",WishSortingGetLabelParam.class);
            if (wishSortingGetLabelParam == null){
                ro.setCode(ReturnObject.MSG_CODE_FALSE);
                ro.setMessage("Bad request");
            }else{
                ServiceReturn serviceReturn = wishSortingLabelService.getLabel(wishSortingGetLabelParam);
                if (serviceReturn.isSuccess()){
                    ro.setCode("0");
                    ro.setMessage(ReturnObject.MESSAGE_SUCCESS);
                    ro.setData(serviceReturn.getData());
                }else{
                    ro.setCode(serviceReturn.getMessageKey());
                    ro.setMessage(serviceReturn.getRemark());
                }
            }
        } catch (Exception e){
            e.printStackTrace();
            ro.setCode("-1");
            ro.setMessage(e.getMessage());
        }
    }

    @Test
    public void getLabelAnother(){
        SortingReturn ro = new SortingReturn();
        ro.setSuccess(true);
        ro.setMessageId(UUID.randomUUID().toString());
        try {
            String reqBody = "{\"data\":{\"facilitycode\":\"08\",\"operateUser\":\"hgh-xqb\",\"trackingNumber\":\"LP00467332550088\",\"referenceNumber\":\"LP00467332550088\",\"packageWeight\":\"1100\",\"scanPackageTime\":\"2021-03-03 16:39:17\",\"packageLength\":null,\"packageWidth\":null,\"packageHeight\":null},\"messageId\":\"20210303163917\",\"timestamp\":\"1614760757000\"}";
            JSONObject jo = JSONObject.parseObject(reqBody);
            WishSortingGetLabelParam wishSortingGetLabelParam = jo.getObject("data",WishSortingGetLabelParam.class);
            if (wishSortingGetLabelParam == null){
                ro.setCode(ReturnObject.MSG_CODE_FALSE);
                ro.setMessage("Bad request");
            }else{
                ServiceReturn serviceReturn = wishSortingLabelService.getLabel(wishSortingGetLabelParam);
                if (serviceReturn.isSuccess()){
                    ro.setCode("0");
                    ro.setMessage(ReturnObject.MESSAGE_SUCCESS);
                    ro.setData(serviceReturn.getData());
                }else{
                    ro.setCode(serviceReturn.getMessageKey());
                    ro.setMessage(serviceReturn.getRemark());
                }
            }
        } catch (Exception e){
            e.printStackTrace();
            ro.setCode("-1");
            ro.setMessage(e.getMessage());
        }
    }

}
