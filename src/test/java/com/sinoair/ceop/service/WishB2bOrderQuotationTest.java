package com.sinoair.ceop.service;

import com.sinoair.ceop.domain.vo.wish.WishB2bOrderQuotationReturnVO;
import com.sinoair.ceop.testUtil.CommonTestCase;
import org.junit.Test;

import javax.annotation.Resource;

public class WishB2bOrderQuotationTest extends CommonTestCase {

    @Resource(name = "WishB2bService")
    private WishB2bService wishB2bService;

    @Test
    public void testWishReport() throws Exception {
        String json = "{\n" +
                "\t\"tracking_numbers\": [\"a1\", \"a2\"]\n" +
                "}";
        WishB2bOrderQuotationReturnVO result = wishB2bService.orderQuotation(json);
        System.out.println("result-------" + result);


    }


}
