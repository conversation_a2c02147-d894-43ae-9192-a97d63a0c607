package com.sinoair.ceop.service;

import com.sinoair.ceop.domain.vo.wish.OrderProcessingReturnVo;
import com.sinoair.ceop.service.impl.CainiaoGlobalSortingcenterOrderinfoNotifyServiceImpl;
import com.sinoair.ceop.testUtil.CommonTestCase;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

public class CainiaoGlobalSortingcenterOrderinfoNotifyServiceImplTest extends CommonTestCase {
    @Autowired
    CainiaoGlobalSortingcenterOrderinfoNotifyServiceImpl cainiaoGlobalSortingcenterOrderinfoNotifyServiceImpl;

    @Test
    public void testAddUser() throws Exception {
        String json = "{\n" +
                "\t\"logisticsEvent\": {\n" +
                "\t\t\"eventHeader\": {\n" +
                "\t\t\t\"eventTime\": \"2017-10-09 20:25:00\",\n" +
                "\t\t\t\"eventTimeZone\": \"UTC+8\",\n" +
                "\t\t\t\"eventType\": \"CONSO_WAREHOUSE_OUTBOUND_NOTICE\"\n" +
                "\t\t},\n" +
                "\t\t\"eventBody\": {\n" +
                "\t\t\t\"logisticsDetail\": {\n" +
                "\t\t\t\t\"popStationDetail\": {\n" +
                "\t\t\t\t\t\"stationType\": \"1\",\n" +
                "\t\t\t\t\t\"stationId\": \"123123\"\n" +
                "\t\t\t\t},\n" +
                "\t\t\t\t\"orderSource\": \"1\",\n" +
                "\t\t\t\t\"totalLogisticsOrderCodes\": \"LP00019884928394|LP00084928498294\",\n" +
                "\t\t\t\t\"bizType\": \"CONSO\",\n" +
                "\t\t\t\t\"sortingCode\": \"01-001\",\n" +
                "\t\t\t\t\"consoWarehouseCode\": \"TRANS_CODE_001\",\n" +
                "\t\t\t\t\"carrierCode\": \"TRANS_CODE_001\",\n" +
                "\t\t\t\t\"logisticsOrderCode\": \"LP00087347294824\",\n" +
                "\t\t\t\t\"receiverDetail\": {\n" +
                "\t\t\t\t\t\"country\": \"中国\",\n" +
                "\t\t\t\t\t\"zipCode\": \"邮编\",\n" +
                "\t\t\t\t\t\"town\": \"乡镇/街道\",\n" +
                "\t\t\t\t\t\"city\": \"九龙\",\n" +
                "\t\t\t\t\t\"mobile\": \"13996974832\",\n" +
                "\t\t\t\t\t\"membership\": {\n" +
                "\t\t\t\t\t\t\"paidType\": \"Y/N\",\n" +
                "\t\t\t\t\t\t\"userLevel\": \"regularMember / seniorMember\"\n" +
                "\t\t\t\t\t},\n" +
                "\t\t\t\t\t\"wangwangId\": \"买家旺旺Id\",\n" +
                "\t\t\t\t\t\"identityNo\": \"身份证号\",\n" +
                "\t\t\t\t\t\"province\": \"香港特别行政区\",\n" +
                "\t\t\t\t\t\"userRecogCode\": \"用户识别码\",\n" +
                "\t\t\t\t\t\"phone\": \"0571-9280324\",\n" +
                "\t\t\t\t\t\"streetAddress\": \"详细地址\",\n" +
                "\t\t\t\t\t\"district\": \"北区\",\n" +
                "\t\t\t\t\t\"name\": \"张三\",\n" +
                "\t\t\t\t\t\"email\": \"<EMAIL>\"\n" +
                "\t\t\t\t},\n" +
                "\t\t\t\t\"outboundLogisticsOrderCodes\": \"67234800011001002|67234800011001003\",\n" +
                "\t\t\t\t\"segmentCode\": \"SENDTORECVER\",\n" +
                "\t\t\t\t\"majorlogisticsOrderCode\": \"LP00087347294824\"\n" +
                "\t\t\t},\n" +
                "\t\t\t\"paymentDetail\": {\n" +
                "\t\t\t\t\"payOrderId\": \"324523457353\",\n" +
                "\t\t\t\t\"payTimeZone\": \"UTC+8\",\n" +
                "\t\t\t\t\"shippingFee\": \"1000\",\n" +
                "\t\t\t\t\"payTime\": \"2017-10-09 20:25:00\",\n" +
                "\t\t\t\t\"payWeight\": \"1000\",\n" +
                "\t\t\t\t\"currency\": \"CNY\",\n" +
                "\t\t\t\t\"currencyUnit\": \"CENT\",\n" +
                "\t\t\t\t\"weightUnit\": \"GRAM\"\n" +
                "\t\t\t},\n" +
                "\t\t\t\"extendData\": {\n" +
                "\t\t\t\t\"promotionActityId\": \"1111111111\",\n" +
                "\t\t\t\t\"promotionMoney\": \"2000\",\n" +
                "\t\t\t\t\"currency\": \"CNY\",\n" +
                "\t\t\t\t\"currencyUnit\": \"CENT\",\n" +
                "\t\t\t\t\"categoryFeature\": \"isLarge:1,\n" +
                "\t\t\t\tisElectric: 1,\n" +
                "\t\t\t\torderValueRange: H \"\n" +
                "\t\t\t}\n" +
                "\t\t}\n" +
                "\t}\n" +
                "}";


        OrderProcessingReturnVo result = cainiaoGlobalSortingcenterOrderinfoNotifyServiceImpl.consoWarehouseOutboundNotice(json);
        System.out.println("result-----------" + result.getResponseMessage());

    }


}

