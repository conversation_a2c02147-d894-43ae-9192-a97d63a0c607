package com.sinoair.ceop.testUtil;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by WangXX4 on 2016/6/22.
 */
public class ClassCreater {
    String root = "";
    String className = "";
    String classBody = "";

    public static void main(String[] args) throws IOException {
        List list = new ArrayList();
        list.add("DISTRIBUTOR_1499269");
        list.add("DISTRIBUTOR_1682692");
        ClassCreater classCreater = new ClassCreater();
        classCreater.setRoot("D:\\idea\\CEOP\\src" +
                "\\test" +
                "\\java\\com\\sinoair\\ceop\\service" +
                "\\webservice\\transmode\\serviceType");
        for (Object s : list) {
            classCreater.setClassName("ServiceType" + s.toString());
            classCreater.setClassBody(
                    "package com.sinoair.ceop.service.transmode.serviceType;\n" +
                            "\n" +
                            "import org.springframework.stereotype.Service;\n" +
                            "\n" +
                            "/**\n" +
                            " * Created by WangXX4 on 2016/6/20.\n" +
                            " */\n" +
                            "@Service(\"" + classCreater.getClassName() + "\")\n" +
                            "public class " + classCreater.getClassName() + " extends ServiceType {\n" +
                            "\n" +
                            "}\n");
            classCreater.create();
        }

    }

    private void create() throws IOException {
        File folder = new File(getRoot());
        if (!folder.exists()) {
            folder.mkdirs();
        }
        final String pathname = getRoot() + "\\" + getClassName() + ".java";
        System.out.println("pathname = " + pathname);
        File file = new File(pathname);
        if (!file.exists()) {
            file.createNewFile();
        }
        FileWriter fileWriter = new FileWriter(file);
        fileWriter.write(getClassBody());
        fileWriter.close();
    }

    public String getRoot() {
        return root;
    }

    public void setRoot(String root) {
        this.root = root;
    }

    public String getClassName() {
        return className;
    }

    public void setClassName(String className) {
        this.className = className;
    }

    public String getClassBody() {
        return classBody;
    }

    public void setClassBody(String classBody) {
        this.classBody = classBody;
    }
}
